<template>
  <div>
    <div id="inlineSearch">
      <div class="row">
        <div
          class="col-12 pb-1"
          :class="{ 'col-md-4': documentationMode === true, 'col-md-6': documentationMode === false }">
          <div v-if="documentationMode === false">
            <typeahead-input
              :src="'/box/nextcloud/search?folder=' + folderPath + '&language=' + languageTld"
              query-param-name="search"
              :placeholder="$t('box/search/placeholder')"
              :responseAttr="['filenameURLDecoded', 'extension', 'languages', 'pathOnlyURLDecoded']"
              :disableReset="true"
              :nothingFoundText="$t('box/search/nothing_found')"
              @hit="onSelectFile"
              cssStyle="box"
              popperPosition="bottom-start"
              delay="500"
              limit="200"
              :serverLimited="true">
              <!--TODO: Die Slots können angepasst werden mit eigenem CSS und co.
                                mit slot-scope wird ein Objekt definiert, über welches man auf den Child-Scope zugreifen kann.
                                Properties je Slot:
                                limited slot: items_count (Anzahl der angezeigten Ergebnisse), max_items (Gesamtzahl der Ergebnisse)
                                empty slot: - bislang keine Properties -
                                item slot: item, $item (aktueller Index), items_count, attributes (array - gewählte Antwort-Attribute)
                                -->
              <template v-slot:limited="slot">
                <li
                  class="limitSlot"
                  @mousedown.prevent
                  v-html="
                    $t('box/search/limited')
                      .replace('#LIMIT#', slot.items_count)
                      .replace('#FOUND#', slot.max_items_server)
                  "></li>
              </template>
            </typeahead-input>
          </div>

          <div v-if="documentationMode === true">
            <typeahead-input
              :src="
                '/box/nextcloud/searchDocumentation?folder=' +
                folderPath +
                '&documentationMode=' +
                documentationMode +
                '&currentOnly=' +
                currentOnly +
                '&archiveOnly=' +
                archiveOnly +
                '&language=' +
                languageTld
              "
              query-param-name="search"
              :placeholder="$t('box/search/placeholder')"
              :responseAttr="['filenameURLDecoded', 'extension', 'languages', 'pathOnlyURLDecoded']"
              :disableReset="true"
              :nothingFoundText="$t('box/search/nothing_found')"
              @hit="onSelectFile"
              cssStyle="box"
              popperPosition="bottom-start"
              delay="500"
              limit="200"
              :serverLimited="true">
              <!--TODO: Die Slots können angepasst werden mit eigenem CSS und co.
                                mit slot-scope wird ein Objekt definiert, über welches man auf den Child-Scope zugreifen kann.
                                Properties je Slot:
                                limited slot: items_count (Anzahl der angezeigten Ergebnisse), max_items (Gesamtzahl der Ergebnisse)
                                empty slot: - bislang keine Properties -
                                item slot: item, $item (aktueller Index), items_count, attributes (array - gewählte Antwort-Attribute)
                                -->
              <template v-slot:limited="slot">
                <li
                  class="limitSlot"
                  @mousedown.prevent
                  v-html="
                    $t('box/search/limited')
                      .replace('#LIMIT#', slot.items_count)
                      .replace('#FOUND#', slot.max_items_server)
                  "></li>
              </template>
            </typeahead-input>
          </div>
        </div>

        <div class="col-12 col-md-3 pb-1">
          <div class="dropdown">
            <button
              id="dropdownLanguage"
              class="btn btn-sm btn-block btn-light dropdown-toggle"
              data-toggle="dropdown"
              type="button"
              aria-haspopup="true"
              aria-expanded="false"
              v-html="languageFlag + language"></button>
            <div class="dropdown-menu" aria-labelledby="dropdownLanguage">
              <a
                class="dropdown-item"
                style="font-weight: bold"
                @click="selectLanguage({ name: $t('box/search/all_languages'), tld: null })">
                {{ $t('box/search/all_languages') }}
              </a>
              <a v-for="language in languages" class="dropdown-item" @click="selectLanguage(language)">
                <img
                  class="flag"
                  :src="
                    require('Flags/png100px/' + language.tld.replace('en', 'gb').replace('eu', 'aq') + '.png')
                  " />&nbsp;&nbsp;{{ language.name }}
              </a>
            </div>
          </div>
        </div>

        <div class="col-12 col-md-3 pb-1">
          <div class="dropdown">
            <button
              id="dropdownFolder"
              class="btn btn-sm btn-block btn-light dropdown-toggle"
              data-toggle="dropdown"
              type="button"
              aria-haspopup="true"
              aria-expanded="false"
              v-html="folderTranslation"></button>
            <div class="dropdown-menu" aria-labelledby="dropdownFolder">
              <a
                v-if="documentationMode === false"
                class="dropdown-item"
                style="font-weight: bold"
                @click="
                  selectFolder({ filename: $t('box/search/all_folders'), fullpath: '/' }, $t('box/search/all_folders'))
                "
                >{{ $t('box/search/all_folders') }}</a
              >
              <a
                v-if="documentationMode"
                class="dropdown-item"
                style="font-weight: bold"
                @click="selectDocumentFolder('', $t('box/search/all_folders'))"
                >{{ $t('box/search/all_folders') }}</a
              >
              <a
                v-for="folder in folders"
                v-if="documentationMode === false && folder.isDir"
                class="dropdown-item"
                @click="selectFolder(folder, folder.filenameURLDecoded)"
                v-html="folder.filenameURLDecoded"></a>
              <a
                v-for="(folder, key) in folders"
                v-if="documentationMode"
                class="dropdown-item"
                @click="selectDocumentFolder(key, $t(folder))"
                v-html="$t(folder)"></a>
            </div>
          </div>
        </div>

        <div v-show="documentationMode" class="col-12 col-md-2 pb-1">
          <div class="dropdown">
            <button
              id="dropdownArchive"
              class="btn btn-sm btn-block btn-light dropdown-toggle"
              data-toggle="dropdown"
              type="button"
              aria-haspopup="true"
              aria-expanded="false"
              v-html="currentOrArchive"></button>
            <div class="dropdown-menu" aria-labelledby="dropdownArchive">
              <a class="dropdown-item" style="font-weight: bold" @click="setCurrentOnly">{{
                $t('box/search/currentOnly')
              }}</a>
              <a class="dropdown-item" @click="setArchiveOnly">{{ $t('box/search/archiveOnly') }}</a>
              <a class="dropdown-item" @click="setCurrentAndArchive()">{{ $t('box/search/currentAndArchive') }}</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue';
import Component from 'vue-class-component';
import { Prop } from 'vue-property-decorator';
import { Action } from 'vuex-class';

import de from 'Translations/across/across.de_DE.json';
import en from 'Translations/across/across.en_GB.json';
import fr from 'Translations/across/across.fr_FR.json';
import es from 'Translations/across/across.es_ES.json';

import TypeaheadInput from '../helper/TypeaheadInput.vue';

import VueI18n from 'vue-i18n';
Vue.use(VueI18n);

const i18n = new VueI18n({
  locale: <string>document.getElementsByTagName('html')[0].getAttribute('lang'),
  messages: { de, en, fr, es },
});

@Component({
  name: 'NextcloudInlineSearch',
  i18n,
  components: {
    TypeaheadInput,
  },
})
export default class NextcloudInlineSearch extends Vue {
  @Prop({ default: null }) startFolder: string;
  @Prop({
    default: function () {
      return [];
    },
  })
  folders: Array<string>;
  @Prop({ default: false }) documentationMode: boolean;

  @Action('box/openFolder') openFolder: any;

  query: string = '';
  folder: string = '';
  folderPath: string = '';
  folderTranslation: string = <string>i18n.t('box/search/all_folders');
  language: string = '';
  languageTld: string = '';
  languageFlag: string = '';
  currentOnly: boolean = true;
  archiveOnly: boolean = false;
  currentOrArchive: string = <string>i18n.t('box/search/currentOnly');

  languages: any = [
    { name: i18n.t('language/brazilian'), tld: 'br' },
    { name: i18n.t('language/german'), tld: 'de' },
    { name: i18n.t('language/english'), tld: 'en' },
    { name: i18n.t('language/danish'), tld: 'dk' },
    { name: i18n.t('language/dutch'), tld: 'nl' },
    { name: i18n.t('language/czech'), tld: 'cz' },
    { name: i18n.t('language/finnish'), tld: 'fi' },
    { name: i18n.t('language/french'), tld: 'fr' },
    { name: i18n.t('language/greek'), tld: 'gr' },
    { name: i18n.t('language/italian'), tld: 'it' },
    { name: i18n.t('language/multi'), tld: 'eu' },
    { name: i18n.t('language/norwegian'), tld: 'no' },
    { name: i18n.t('language/polish'), tld: 'pl' },
    { name: i18n.t('language/swedish'), tld: 'se' },
    { name: i18n.t('language/spanish'), tld: 'es' },
    { name: i18n.t('language/turkish'), tld: 'tr' },
  ];

  /**
   * Callback Funktion die aufgerufen wird, wenn auf ein Suchergebnis geklickt wird
   *
   * @param file
   */
  private onSelectFile(file: any): void {
    this.openFolder(file.fullpathBase64encoded);
  }

  setQuery(query: string): void {
    this.query = query;
  }

  public selectLanguage(language: any): void {
    this.language = language.name;
    this.languageTld = language.tld;
    this.languageFlag =
      language.tld !== null
        ? '<img src="' +
          require('Flags/png100px/' + language.tld.replace('en', 'gb').replace('eu', 'aq') + '.png') +
          '" class="flag" />'
        : '';
  }

  public selectFolder(folder: any, folderTranslation: string): void {
    this.folderTranslation = folderTranslation;
    this.folder = folder.filename;
    this.folderPath = folder.fullpath;
  }

  public selectDocumentFolder(folder: string, folderTranslation: string): void {
    this.folderTranslation = folderTranslation;
    this.folder = folder;
    this.folderPath = folder;
  }

  public setArchiveOnly(): void {
    this.archiveOnly = true;
    this.currentOnly = false;
    this.currentOrArchive = <string>i18n.t('box/search/archiveOnly');
  }

  public setCurrentOnly(): void {
    this.currentOnly = true;
    this.archiveOnly = false;
    this.currentOrArchive = <string>i18n.t('box/search/currentOnly');
  }

  public setCurrentAndArchive(): void {
    this.currentOnly = false;
    this.archiveOnly = false;
    this.currentOrArchive = <string>i18n.t('box/search/currentAndArchive');
  }

  created(): void {
    this.folderPath = this.startFolder !== null ? this.startFolder : '/';

    this.folder = <string>i18n.t('box/search/all_folders');
    this.language = <string>i18n.t('box/search/all_languages');
  }
}
</script>

<style lang="scss" scoped>
@import '@assets/public/vendor/dynamicTheme/css/bootstrapCustomScss/custom-variables.scss';

#inlineSearch {
  padding: 15px 10px;
  background-color: $abus-grey-7;
  color: $white;
  border-top-left-radius: 0.3em;
  border-top-right-radius: 0.3em;

  .dropdown {
    button {
      height: 37px;
    }
    img {
      margin-top: -5px;
    }
  }
}

#file {
  padding: 15px 10px;
  border-top: #dfdfdf 1px solid;
  background-color: $abus-blue-light;
  color: $white;
  display: flow-root;

  div.filename {
    float: left;
    margin-right: 20px;
    line-height: 30px;
  }

  div.buttons {
    float: right;
  }

  button {
    margin-left: 15px;
  }
}
</style>

<style lang="scss">
div#inlineSearch .dropdown button img {
  margin-top: -3px;
  margin-right: 10px;
}

div#inlineSearch .flag {
  width: 20px;
  height: 13px;
}
</style>
