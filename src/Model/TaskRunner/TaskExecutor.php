<?php
/**
 * <PERSON><PERSON><PERSON>: <PERSON>
 * E-Mail: <EMAIL>
 * Datum: 20.06.17 12:52.
 */

namespace App\Model\TaskRunner;

use App\Entity\TaskRunner\Task;
use Doctrine\ORM\EntityManager;

class TaskExecutor
{
    /** @var EntityManager */
    private $entityManager;

    /** @var TaskMailer */
    private $taskMailer;

    /** @var array */
    private $taskServices;

    /**
     * TaskRunner constructor.
     */
    public function __construct(EntityManager $entityManager, TaskMailer $taskMailer, array $taskServices)
    {
        $this->entityManager = $entityManager;
        $this->taskMailer = $taskMailer;
        $this->taskServices = $taskServices;
    }

    /**
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function runAllOpenTasks()
    {
        dd('runTask 0');
        foreach ($this->getOpenTasks() as $task) {
            $this->runTask($task);
        }
    }

    /**
     * Führt eine bestimmte Aufgabe aus.
     *
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     * @throws \Exception
     */
    private function runTask(Task $task): bool
    {
        dd('runTask');
        try {
            // TODO Der Wert sollte an die Realtität angepasst werden
//            ini_set('memory_limit', '-1');
            //	        ini_set('max_execution_time', 3600);

            echo time().'Starte Task '.$task->getBundleName()."\n";

            $task->setCrawlerStart(new \DateTime()); //Startzeit setzen
            $task->setRunning(true);
            $this->entityManager->persist($task);
            $this->entityManager->flush();
            dd('runTask 2');

            $result = $this->taskServices[$task->getBundleName()]->runTask($task);

            $task->setStatistics($result);

            echo time().'Beende Task '.$task->getBundleName().":\n";
            //var_dump($result);

            $task->setCrawlerEnd(new \DateTime()); //Endzeit setzen
            $task->setRunning(false);
            $this->entityManager->persist($task);
            $this->entityManager->flush();

            echo time().'Sende Mail Task '.$task->getBundleName().' beendet an '.$task->getUsername()."\n";
            $this->taskMailer->sendMail($task, $this->taskServices[$task->getBundleName()]->resultToMail($task));

            return true;
        } catch (\Exception $e) {
            $task->setRunning(false);
            $task->setError(
                json_encode(
                    [
                        'message' => $e->getMessage(),
                        'file' => $e->getFile(),
                        'line' => $e->getLine(),
                    ]
                )
            );
            $this->entityManager->persist($task);
            $this->entityManager->flush();

            $this->taskMailer->sendMail($task, $e->getMessage());

            // Fehler in der Konsole ausgeben
            throw $e;
        }
    }

    /**
     * Findet alle offenen Aufgaben.
     */
    private function getOpenTasks(): array
    {
        //TODO Dublicates erkennen und dann nur einmal ausführen, aber ggf an mehrere Adressen das Resultat schicken

        return $this->entityManager->getRepository(Task::class)->findBy(
            [
                'running' => false,
                'crawlerstart' => null,
            ]
        );
    }
}
