<?php

/**
 * <PERSON><PERSON><PERSON>: <PERSON>
 * E-Mail: <EMAIL>
 * Datum: 20.06.17 14:27.
 */

namespace App\Model\Box\TaskRunner;

use App\Entity\TaskRunner\Task;
use App\Exception\TaskRunnerException;
use App\Model\Box\Crawler\Crawler;
use App\Model\Box\Nextcloud\ConfigParser\ConfigParser;
use App\Model\Box\Nextcloud\Tagger\Tagger;
use App\Model\TaskRunner\TaskRunnerInterface;
use Twig\Environment;

/**
 * Führt einen Task in der Box aus.
 *
 * Class TaskRunner
 */
class TaskRunner implements TaskRunnerInterface
{
    /** @var Crawler */
    private $crawler;

    /** @var Tagger */
    private $tagger;

    /** @var ConfigParser */
    private $configParser;

    private Environment $twigEngine;

    /**
     * TaskRunner constructor.
     */
    public function __construct(Crawler $crawler, Tagger $tagger, ConfigParser $configParser, Environment $twigEngine)
    {
        $this->crawler = $crawler;
        $this->tagger = $tagger;
        $this->configParser = $configParser;
        $this->twigEngine = $twigEngine;
    }

    /**
     * Führt den Task aus und ruft den Crawler auf.
     *
     * @throws TaskRunnerException
     * @throws \App\Exception\FileInDatabaseNotFoundException
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     * @throws \App\Exception\BrokenLinkException
     */
    public function runTask(Task $task): array
    {
        $parameters = $task->getParameters();

        dd($parameters);

        switch ($task->getTask()) {
            case 'crawler':
                if (isset($parameters['path']) && isset($parameters['pdf'])) {
                    return (array) $this->crawler->crawl($task);
                }

                throw new TaskRunnerException('Missing parameters "path" or "pdf"');
            case 'nextcloudTags':
                return ['Tags hinzugefügt:' => $this->tagger->tag()];

            case 'nextcloudIndex':
                if (isset($parameters['enrichForSearch'])) {
                    return ['Indizierung abgeschlossen:' => $this->configParser->parse($parameters['enrichForSearch'])];
                }

                throw new TaskRunnerException('Missing parameters "path" or "pdf"');
        }

        return ['Error' => 'Task '.$task->getTask().' not found'];
    }

    /**
     * Generiert den Mail Inhalt.
     *
     * @throws \Twig_Error_Loader
     * @throws \Twig_Error_Runtime
     * @throws \Twig_Error_Syntax
     */
    public function resultToMail(Task $task): string
    {
        $mailBody = '<table style="font-family: sans-serif;">';
        foreach ($task->getStatistics() as $key => $value) {
            if (!\is_array($value)) {
                $mailBody .= '<tr><td>'.ucfirst($key).'</td><td>'.$value.'</td></tr>';
            } else {
                $mailBody .= '<tr><td>'.ucfirst($key).'</td><td>';
                foreach ($value as $subkey => $subvalue) {
                    $mailBody .= $subvalue.'<br />';
                }
                $mailBody .= '</td></tr>';
            }
        }
        $mailBody .= '</table>';

        return $mailBody;

        return $this->twigEngine->render(
            'ABUSBoxBundle:TaskRunner/Email:Crawler.html.twig',
            ['statistics' => $task->getStatistics()]
        );
    }
}
